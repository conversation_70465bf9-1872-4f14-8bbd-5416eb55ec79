create or replace view gestalt_client_db.config.loanpro_events_payments_reversed (
    customer_key
    , loan_id
    , payment_id
    , updated_at
) as
select
    src_j_1.key as customer_key
    , src.data:context."loan-id" as loan_id
    , src.data:context."last-reversed-payment-id" as payment_id
    , src.updated_at
from gestalt_client_db.config.source_data_events as src
inner join gestalt_client_db.config.customers as src_j_1
on src.customer_id = src_j_1.id
where src.type_entity = 'payment'
    and src.source = 'loanpro'
    and src.data_category = 'payment_reversed'
    and src.deleted_at is null;
