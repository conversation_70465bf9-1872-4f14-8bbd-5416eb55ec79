create or replace view gestalt_client_db.config.loanpro_events_loan_collateral (
    customer_key
    , loan_id
    , updated_at
) as
select
    src_j_1.key as customer_key
    , src.data:context."loan-id" as loan_id
    , src.updated_at
from gestalt_client_db.config.source_data_events as src
inner join gestalt_client_db.config.customers as src_j_1
on src.customer_id = src_j_1.id
where type_entity = 'loan'
    and data_category = 'loan_collateral'
    and source = 'loanpro'
    and deleted_at is null;