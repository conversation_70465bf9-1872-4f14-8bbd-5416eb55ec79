import json
from time import sleep
import requests
import hashlib
import math
from datetime import datetime
import os
import boto3
import traceback
import logging

# REST API Handler CLASS
class RestAPIRepository():
    def __init__(self, base_url):
        self.base_url = base_url

    def _handle_response(self, response):
        content_type = response.headers.get("Content-Type", "")
        if "application/json" in content_type:
            return response.json(), response.headers
        else:
            return response.text, response.headers

    def get(self, resource_id, headers=None):
        response = requests.get(f"{self.base_url}/{resource_id}", headers=headers)
        if response.status_code == 200:
            return self._handle_response(response)
        else:
            print(f"GET Failed with response code = {response.status_code}")
            return None

    def create(self, data, resource_id=None, headers=None):
        if resource_id is None:
            response = requests.post(self.base_url, json=data, headers=headers)
        else:
            response = requests.post(f"{self.base_url}/{resource_id}", json=data, headers=headers)
        if response.status_code == 200:
            return self._handle_response(response)
        else:
            print(f"Error: Code returned {response.status_code}")
            raise Exception("CREATE failed")

    def update(self, resource_id, data, headers=None):
        response = requests.put(f"{self.base_url}/{resource_id}", json=data, headers=headers)
        return response.json()

    def delete(self, resource_id, headers=None):
        response = requests.delete(f"{self.base_url}/{resource_id}", headers=headers)
        return response.status_code == 204

# RENT MANAGER WRAPPERS APIs

# RENT MANAGER authentication
def authenticate_rm_api(repository, username, password):
    custom_headers = {'Content-Type': 'application/json'}
    data_to_create = {'UserName': username, 'Password': password}
    created_resource, header = repository.create(data_to_create, "Authentication/Authorizeuser", headers=custom_headers)
    token = created_resource.strip('"')
    #print(f"Token returned => {token}")
    return token

# RENT MANAGER GET
def fetch_data_rm_api(repository, token, resource_id):
    custom_headers = {'x-rm12api-apitoken': token}
    retrieved_resource = repository.get(resource_id, headers=custom_headers)
    
    #print("Retrieved resource:", retrieved_resource)
    return retrieved_resource

# Calculate HASH Key for a given row; Store HASH key for each record in METADATA section
def get_hash_key(record):
    return hashlib.sha256(json.dumps(record, indent=None, ensure_ascii=False).encode()).hexdigest()

# Function to store data in s3
def push_data_s3(table_name, request, json_str, current_ts):
    client = boto3.client('s3')

    bucket = request['customPayload']['s3_bucket']

    # Get Current TS to suffix with the filename
    current_ts = current_ts.strftime("%d%m%Y_%H%M%S")
    
    # Convert JSON to Python dictionary
    data = json.loads(json_str)
    
    output = {str(i+1): asset for i, asset in enumerate(data)}
    file_name = f"{request['customPayload']['s3_key']}{table_name}_{current_ts}.json"
    client.put_object(Body=json.dumps(output), Bucket=bucket, Key=file_name)

# Fivetran expects a response in specific format to be able to UPSERT the data
# Refer to : https://fivetran.com/docs/connectors/functions/aws-lambda#responseformat
def create_fivetran_response(data, event, current_ts, hasMore_flag):

    # Extract and parse 'resources' as JSON
    resources_str = event['customPayload']['resources']
    resources = json.loads(resources_str)
    current_state = event['state']

    # initialize the current state received.
    formatted_data = {
            "state": current_state,
            "insert": {},
            "delete": {},
            "schema": {},
            "hasMore": hasMore_flag,
            "softDelete": []  # Add the tables to be soft deleted here
        }

    for table_name, table_info in data.items():
        records_info = table_info["records"]

        formatted_timestamp = current_ts.strftime('%Y-%m-%dT%H:%M:%SZ') #%Y-%m-%dT%H:%M:%SZ        
        source_timestamp = formatted_timestamp

        # Response Object "STATE"        
        for resource in resources:
            if table_name == resource['table_name']:
                p_key = resource['primary_key']
                # Response Object "STATE" for returning the last offset
                formatted_data["state"][table_name] = records_info[-1][p_key]

                # Response Object "SCHEMA" for returning the PRIMARY_KEY
                formatted_data["schema"][table_name] = {"primary_key": [p_key]}
        
        # Response Object "DELETE"
        formatted_data["delete"][table_name] = []
        
        # Convert keys to lowercase and replace spaces with underscores
        formatted_records = []
        
        # Generate filename for metadata and S3 file
        sourece_filename_ts = current_ts.strftime("%d%m%Y_%H%M%S")
        source_filename = f"{table_name}_{sourece_filename_ts}.json"

        for record_data in records_info:
            for resource in resources:
                if table_name == resource['table_name']:
                    p_key = resource['primary_key']
                    formatted_record = {
                        "hash_key": "",
                        p_key:record_data[p_key],
                        "metadata": {
                            "source_name": table_name,
                            "source_timestamp": source_timestamp,
                            "source_filename": source_filename,
                            "source_type": "json"
                        },
                        "record": {}
                    }

            for key, value in record_data.items():
                formatted_key = key.lower().replace(" ", "_")
                formatted_record["record"][formatted_key] = value
                # Add the hash key to the metadata section of the record.
                formatted_record['hash_key'] = get_hash_key(formatted_record['record'])
                #formatted_record[p_key] = formatted_record['record'][p_key]
            
            # Append the formatted record
            formatted_records.append(formatted_record)
        
        formatted_data["insert"][table_name] = formatted_records
        
    return formatted_data

"""
NOTE (Refer to the API Documentation): 
Pagination functionality is provided through the use of the pagenumber and pagesize query string parameters.
Any resource that returns more than 1000 records will automatically convert to using pagination. 
If pagenumber is specified and pagesize is greater than 1000 or is not provided pagesize will be set to 1000.
When using pagination the HTTP response headers will include additional information about the pagination.

The "X-Total-Results" header will contain the total number of results that were found from the request.
"""

# Main Driver Code
def get_resources_data(event, current_ts, page_number=1, page_size=1000):

    # Rent Manager Base URL 
    api_base_url = "https://metra.api.rentmanager.com"

    # Create REST API instance for the provided base URL
    repository = RestAPIRepository(base_url=api_base_url)

    # Fetch REQUEST data received Fivetran
    current_state = event['state']
    print(f'Current STATE Received :\n{current_state}')

    # Extract and parse 'resources'; Fivetrans sends its as JSON String
    resources_str = event['customPayload']['resources']
    resources = json.loads(resources_str)    
    
    # This is added to handle the Scenario when we have added new TABLES but there are no STATE saved
    # Initialize to 0, otherwise the pipeline wont pick up the table
    for resource in resources:
        if resource['table_name'] not in current_state:
            print(f"{resource['table_name']} NOT Found in STATE")
            current_state[resource['table_name']] = 0
        
    print(f'UPDATED STATE :\n{current_state}')

    
    # Passed from Fivetran pipeline
    username = event['secrets']['username']
    password = event['secrets']['password']    

    data = {}
    hasMore_flag = False

    # Step 1: Authenticate
    token = authenticate_rm_api(repository, username, password)

    # Step 2: Fetch Data; Loop Through the TABLES passed as a JSON List by Fivetran
    for resource in resources:
        table_name = resource['table_name']
        
        print(f"Starting the PROCESSING FOR {table_name}")
        
        # If Fivetran Sends any saved state, then its not HISTORICAL 
        if current_state:
            if table_name in current_state:
                print(f'STARTING INCREMENTAL PULL FOR : {table_name}')
                if 'NoFilteringSupport' in resource:
                    print('No suport for Filtering..')
                    result = fetch_data_rm_api(repository, token, f"{table_name}?pagesize={page_size}&pagenumber={page_number}")
                else:
                    result = fetch_data_rm_api(repository, token, f"{table_name}/Search?OrderingOptions={resource['primary_key']}&filterExpression={resource['primary_key']},gt,{current_state[table_name]}&pagesize={page_size}&pagenumber={page_number}")
                if result is not None:
                    table_data, headers= result
                else: 
                    continue
            else:
                continue
        else: # Historical Pull
            print(f'STARTING HISTORICAL PULL FOR : {table_name}')

            result = fetch_data_rm_api(repository, token, f"{table_name}?pagesize={page_size}&pagenumber={page_number}")
            if result is not None:
                table_data, headers= result
            else:
                continue
        
        # Get the Total Number of Records exisiting at the SOURCE
        total_results = int(headers.get('X-Total-Results', 0))
        print(f"Total Results Are => {total_results}")
        
        if total_results>=1000:
            hasMore_flag = True    

        # Reduce the result size for DEV
        # Manual THROTTLE to control the result Set
        PAGINATE = False

        # If total results exceed page size, paginate
        if PAGINATE and total_results > page_size:
            num_pages = math.ceil(total_results / page_size)
            for i in range(2, num_pages + 1):
                records_page, _ = fetch_data_rm_api(repository, token, f"{table_name}?pagesize={page_size}&pagenumber={i}")
                table_data.extend(records_page)

        data[table_name] = {
            "records": table_data
        }
        
        # WRITE Individual Table Data to S3
        json_str = bytes(json.dumps(table_data).encode("utf-8"))
        push_data_s3(table_name, event, json_str, current_ts)

    return data, hasMore_flag

logger = logging.getLogger()
logger.setLevel(logging.ERROR)

def lambda_handler(event, context):
    try:
        print(f'REceived EVENT by Lambda => \n{event}')
        
        # Get Current TS
        now = datetime.now()
        
        data, hasMore_flag = get_resources_data(event, now)
        processed_records = create_fivetran_response(data, event, now, hasMore_flag)

        #print(f'Sending This Back to Fivetran : \n{processed_records}')
        print(f"SAVED STATE FOR THE LAST RUN at {now.strftime('%d%m%Y_%H%M%S')}: \n{processed_records['state']}")
        return processed_records

    except Exception as e:
        # Log the exception and its stack trace
        print("An error occurred: ", e)
        print("Stack trace: ", traceback.format_exc())
        
        # Format the error response
        error_response = {
            'statusCode': 500,
            'body': str(e)
        }
        return error_response
