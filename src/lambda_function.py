import io
import logging
import traceback
from datetime import datetime
from fivetran_utils import create_fivetran_sync_through_response, create_fivetran_test_response
from s3_utils import push_data_s3
from rest_api_repository import RestAPIRepository
from config import get_config
from data_processor import DataProcessor

def lambda_handler(event, context):
    try:
        config = get_config(event)
        logger = logging.getLogger()
        logger.setLevel(config.log_level)

        # Print the received CONFIG from Fivetran
        config.print_config()

        # Current Timestamp for bookmarking the events
        now = datetime.now()
        current_ts = now.strftime('%Y-%m-%d %H:%M:%S')

        if config.setup_test:
            test_response = create_fivetran_test_response(True)
            print(f"SETUP CONNECTOR TEST DETECTED AT {current_ts}.")
            print(f"RETURNING TEST RESPONSE: \r{test_response}")
            return test_response
        elif config.current_state.get('connection_test', False):
            test_response = create_fivetran_test_response(False)
            print(f"SETUP CONNECTOR TEST DETECTED AT {current_ts}.")
            print(f"RETURNING TEST RESPONSE: \r{test_response}")
            return test_response
        else:
            # Based on the PARTNER key, import the appropriate module
            print(f'RUNNING MODULE: {config.partner_key}')
            if config.partner_key == 'rentmanager':
                from rentmanager import authenticate, fetch_data, get_resources_data        
            elif config.partner_key == 'loanpro':
                from loanpro import authenticate, fetch_data, get_resources_data
            elif config.partner_key == 'defilos':
                from defilos import get_resources_data
            else:
                raise ValueError(f"Partner Key {config.partner_key} not supported.")

            repository = RestAPIRepository(config)
            data, has_more, current_state = get_resources_data(repository, config, now)
            processed_records = create_fivetran_sync_through_response(data, config, now, has_more, current_state)

            print(f"SAVED STATE FOR THE LAST RUN AT {current_ts}: {processed_records['state']}")
            print(f"FIVETRAN RESPONSE: \r{processed_records}")
            return processed_records

    except Exception as e:
        error_response = {
            'statusCode': 500,
            'body': str(e)
        }
        # Catch the exception and capture the stack trace without newline characters.
        error_buffer = io.StringIO()
        traceback.print_exc(file=error_buffer)

        # Get the error message without newline characters.
        error_message = error_buffer.getvalue().strip().replace('\n', '\r')

        # Print or use the error message without newline characters.
        print(f"Error processing: \r{str(e)}\r\rStack Trace: \r{error_message}")
        return error_response
