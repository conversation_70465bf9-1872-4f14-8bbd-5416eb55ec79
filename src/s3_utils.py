import json
import boto3
import io
import traceback
import botocore.exceptions
from datetime import datetime, timezone

def add_metadata(metadata_obj):
    # Copy object with modified metadata to the destination directory.
    if len(metadata_obj['Metadata']) > 0 and 'gestalt-create-timestamp-utc' in metadata_obj['Metadata']:
        # Create a copy of the existing metadata, add the new key.
        metadata_restore_key = 'gestalt-restore'
        metadata_restore_datetime_key = 'gestalt-restore-timestamp-utc'
        metadata = metadata_obj['Metadata'].copy()
        metadata[metadata_restore_key] = "true"
        metadata[metadata_restore_datetime_key] = datetime.now(timezone.utc).isoformat()
    else:
        # A new file, create the new metadata, add the new key.
        object_datetime_iso = metadata_obj['LastModified'].replace(microsecond=0).replace(tzinfo=timezone.utc).isoformat()
        metadata_key = 'gestalt-create-timestamp-utc'
        metadata = metadata_obj['Metadata'].copy()
        metadata[metadata_key] = object_datetime_iso

    return metadata

def push_data_s3(table_name, config, json_str, current_ts, is_historical_sync=False):
    """This is a function to store data in s3 bucket
    Args:
        table_name (string): Table name
        config (object): The config object initialized with the config file
        json_str (string): It is a json string of content to be stored in s3
        current_ts (datetime): The datetime object of current time
        is_historical_sync (bool, optional): Flag to indicate if the sync is historical. Defaults to False.
    """
    client = boto3.client('s3')

    bucket = config.s3_bucket
    s3_key_prefix = config.s3_key_prefix
    s3_filename_identifier = config.s3_filename_identifier

    data = json.loads(json_str)
    output = {str(i+1): asset for i, asset in enumerate(data)}
    file_name = f"{s3_key_prefix}{table_name}_{s3_filename_identifier}_{current_ts.strftime('%d%m%Y_%H%M%S')}.json"
    try:
        metadata_obj = client.head_object(Bucket=bucket, Key=file_name)
    except botocore.exceptions.ClientError as e:
        # Catch the exception and capture the stack trace without newline characters.
        error_buffer = io.StringIO()
        traceback.print_exc(file=error_buffer)
        # Get the error message without newline characters.
        error_message = error_buffer.getvalue().strip().replace('\n', '\r')
        # Print or use the error message without newline characters.
        print(f"Error processing: \r{str(e)}\r\rStack Trace: \r{error_message}")
        # Assign the current timestamp to the last modified time if the file does not exist.
        metadata_obj = {'Metadata': {}, 'LastModified': current_ts}
    metadata = add_metadata(metadata_obj)
    print(f"Metadata: {metadata}")
    print(f'JSON FILE NAME => {file_name}')
    client.put_object(Body=json.dumps(output), Bucket=bucket, Key=file_name, Metadata=metadata)

def sync_through_s3(config, json_str):
    """This is a function to store data in s3 bucket

    Args:
        request (json): Json object of request
        json_str (bytes): Data to store in s3
    """
    client = boto3.client('s3')
    print(f'SAVING SYNC THROUGH OBJECT => BUCKET: {config.sync_through_s3_bucket} | KEY: {config.sync_through_s3_key}')
    client.put_object(
        Body=json_str, Bucket=config.sync_through_s3_bucket, Key=config.sync_through_s3_key)

def list_s3_files(bucket, prefix, start_time=None, end_time=None):
    """List files in an S3 bucket with a specific prefix and time window.
    This function uses a paginator to handle large result sets and filters files based on the last modified time.

    Args:
        bucket (str): The name of the S3 bucket.
        prefix (str): The prefix to filter the files.
        start_time (datetime, optional): The start time for filtering files
        end_time (datetime, optional): The end time for filtering files.
    Yields:
        tuple [str, datetime]: A tuple containing the S3 object key and its last_modified_time(datetime).
        
    """
    s3 = boto3.client('s3')
    paginator = s3.get_paginator('list_objects_v2')
    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        for obj in page.get('Contents', []):
            key = obj['Key']
            metadata_obj = s3.head_object(Bucket=bucket, Key=key)
            last_modified = datetime.fromisoformat(metadata_obj['Metadata'].get('gestalt-create-timestamp-utc', None))
            # Skip files outside the specified time window
            if start_time and last_modified < start_time:
                continue
            if end_time and last_modified > end_time:
                continue
            yield key, last_modified

def read_file(bucket, key, format='json'):
    """Read a file from S3 and return its content.
    -- Need to update it to handle different formats like CSV, XML, etc.
    Args:
        bucket (str): The name of the S3 bucket.
        key (str): The S3 object key.
        format (str): The format of the file. Default is 'json'.
    """
    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=bucket, Key=key)
    content = response['Body'].read().decode('utf-8')
    if format == 'json':  
        data = json.loads(content)
    
    return data