import hashlib
import json
from s3_utils import sync_through_s3


def get_hash_key(record):
    return hashlib.sha256(json.dumps(record, indent=None, ensure_ascii=False).encode()).hexdigest()


def create_fivetran_test_response(connection_test: bool) -> dict:
    if connection_test:
        formatted_fivetran_response = {
            "state": {"connection_test": True},
            "schema": {},
            "hasMore": False,
            "softDelete": []
        }
    else:
        formatted_fivetran_response = {
            "state": {},
            "schema": {},
            "hasMore": False,
            "softDelete": []
        }

    return formatted_fivetran_response

def create_fivetran_sync_through_response(data, config, current_ts, has_more, current_state):
    resources = config.resources

    # Sync Through S3 Response
    formatted_s3_response = {
        "insert": {},
        "delete": {},
    }

    # Sync Through Fivetran Response
    formatted_fivetran_response = {
        "state": current_state,
        "schema": {},
        "hasMore": has_more,
        "softDelete": []
    }

    for table_name, table_info in data.items():
        print(f'PROCESSING TABLE = {table_name}')
        # Record information variables.
        current_resource = list(filter(lambda x: x['table_name'] == table_name, resources))[0]
        records_info = table_info["records"]
        formatted_timestamp = current_ts.strftime('%Y-%m-%dT%H:%M:%SZ')
        source_timestamp = formatted_timestamp
        primary_key = current_resource['primary_key'] if isinstance(current_resource['primary_key'], list) else [current_resource['primary_key']]
    
        # Output variables.
        formatted_fivetran_response["schema"][table_name] = {"primary_key": primary_key}
        formatted_s3_response["delete"][table_name] = []
        source_filename = f"{table_name}_{config.s3_filename_identifier}_{current_ts.strftime('%d%m%Y_%H%M%S')}.json"
        formatted_records = []

        for record_data in records_info:
            # Initialize the default formatted record and add primary keys.
            # Primary keys are preferred at the top (table level) rather than inside the nested record.
            # We populate the primary keys that are already present in the source data.
            formatted_record = {"hash_key": ""}
            for key in primary_key:
                if not key.startswith("_gestalt"):
                    formatted_record[key] = record_data[key]
            
            # Add metadata and standard fields to the formatted record.
            formatted_record.update({"metadata": { "source_name": table_name, "source_timestamp": source_timestamp, "source_filename": source_filename, "source_type": "json"}, "record": {}})

            # Populate the record data.
            # - Fields introduced by us (custom fields) are prefixed with "_gestalt" and kept at the top level.
            # - Original source fields are placed inside the "record" dictionary.
            # - This separation ensures that "record" contains only the original source data.
            for key, value in record_data.items():
                formatted_key = key.lower().replace(" ", "_")
                if formatted_key.startswith("_gestalt"):
                    formatted_record[formatted_key] = value
                else:
                    formatted_record["record"][formatted_key] = value
                formatted_record['hash_key'] = get_hash_key(formatted_record['record'])
            
            formatted_records.append(formatted_record)
        
        formatted_s3_response["insert"][table_name] = formatted_records
    
    # Write to S3 for Fivetran Sync through method
    json_str = bytes(json.dumps(formatted_s3_response).encode("utf-8"))
    sync_through_s3(config, json_str)

    return formatted_fivetran_response


def create_fivetran_sync_directly_response(data, config, current_ts, has_more, current_state):
    resources = config.resources

    formatted_fivetran_response = {
        "state": current_state,
        "insert": {},
        "delete": {},
        "schema": {},
        "hasMore": has_more,
        "softDelete": []
    }

    for table_name, table_info in data.items():
        print(f'PROCESSING TABLE = {table_name}')
        # Record information variables.
        current_resource = list(filter(lambda x: x['table_name'] == table_name, resources))[0]
        records_info = table_info["records"]
        formatted_timestamp = current_ts.strftime('%Y-%m-%dT%H:%M:%SZ')
        source_timestamp = formatted_timestamp
        primary_key = current_resource['primary_key'] if isinstance(current_resource['primary_key'], list) else [current_resource['primary_key']]

        # Output variables.
        formatted_fivetran_response["schema"][table_name] = {"primary_key": primary_key}
        formatted_fivetran_response["delete"][table_name] = []
        source_filename = f"{table_name}_{config.s3_filename_identifier}_{current_ts.strftime('%d%m%Y_%H%M%S')}.json"
        formatted_records = []

        for record_data in records_info:
            # Setup the default record, adding the primary key(s).
            formatted_record = {"hash_key": ""}
            for key in primary_key:
                formatted_record[key] = record_data[primary_key]
            
            # Add metadata and default elements to the records.
            formatted_record.update({"metadata": { "source_name": table_name, "source_timestamp": source_timestamp, "source_filename": source_filename, "source_type": "json"}, "record": {}})

            # Add the record values and hash key to the record.
            for key, value in record_data.items():
                formatted_key = key.lower().replace(" ", "_")
                formatted_record["record"][formatted_key] = value
                formatted_record['hash_key'] = get_hash_key(formatted_record['record'])
            
            formatted_records.append(formatted_record)
        
        formatted_fivetran_response["insert"][table_name] = formatted_records

    return formatted_fivetran_response
