from rest_api_repository import RestAPIRepository
from s3_utils import push_data_s3
from snowflake_utils import execute_query
from datetime import datetime, timezone, time, timedelta, timezone
import math
import json


def authenticate(repository, lp_bearer, tenantid):
    custom_headers = {'Content-Type': 'application/json'}
    data_to_create = {'Authorization': f'Bearer {lp_bearer}', 'Autopal-Instance-Id': f'{tenantid}'}
    try:
        created_resource, header = repository.create(data_to_create, headers=custom_headers)
        token = created_resource.strip('"')
        return token
    except Exception as e:
        print(f"Authentication failed: \r{e}")
        raise


def epoch_to_timestamp(epoch_value):
    try:
        # Get the epoch value from the Loanpro string, or set a default if none.
        default_epoch = int(datetime.combine(datetime.now(timezone.utc).date(), time.min, timezone.utc).timestamp())
        epoch_value = int(epoch_value[6:-2]) if epoch_value else default_epoch

        ett = datetime.fromtimestamp(epoch_value, timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')
        return ett
    except ValueError:
        raise ValueError(f"Invalid format for epoch_value string: {epoch_value}")


def get_source_data_events(primary_key, events_table_name, last_primary_key, last_updated_at, customer_key):
    ## READ EVENTS FROM SNOWFLAKE
    max_events = 300
    query = f"""
        with population_cte as (
            select
                updated_at
                , {primary_key}
            from gestalt_client_db.config.{events_table_name}
            where updated_at >= '{last_updated_at}' and {primary_key} != '{last_primary_key}' and customer_key = '{customer_key.lower()}'
            qualify row_number() over (partition by {primary_key} order by updated_at desc) = 1
        )
        select
            array_agg(object_construct('primary_key', {primary_key}, 'updated_at', updated_at)) within group (order by updated_at) AS record_list
        from population_cte
    """
    print_query = " ".join(query.split()).strip()
    print(f"Executing Source Events Query Statement:\r{print_query}")
    
    # Execute the query
    result = execute_query(query, customer_key)

    # Extract result values
    if result and len(result) > 0:
        row = result[0]
        record_list = json.loads(row.RECORD_LIST)

        if len(record_list) > 0:
            # Log and determine if return list needs to be sized down.
            print(f"Fetched {len(record_list)} records for source events tracking.")
            if len(record_list) > max_events:
                print(f"Reducing size to max events of {max_events} records.")
                return record_list[:max_events], True

            return record_list, False

    # Return empty values if no result found
    print("No records returned for source events tracking.")
    return [], False


def fetch_data(repository, config, resource_id, expected_length):
    custom_headers = {'Authorization': f'Bearer {config.lp_bearer}', 'Autopal-Instance-Id': f'{config.tenantid}', 'Content-Type': 'application/json'}
    try:
        retrieved_resource = repository.get(resource_id, headers=custom_headers)
        if retrieved_resource is None:
            print(f"No data fetched for {resource_id}.")
            return [], False
        else:
            results = retrieved_resource[0].get('d', {}).get('results', [])
            print(f"Fetched {len(results)} records for {resource_id}")
            if len(results) == expected_length:
                hasMore_flag = True
            else:
                hasMore_flag = False
            return results, hasMore_flag
    except Exception as e:
        print(f"Failed to fetch data: {e}")
        raise


def get_resources_data(repository, config, current_ts):
    # Build out function variables.
    data = {}
    current_state = config.current_state
    resources = config.resources
    has_current_state = len(current_state) > 0
    next_resource = False
    first_resource_name = resources[0]['name']
    last_resource_name = resources[-1]['name']

    # Build out a dictionary of current state to return if not passed.
    # This is a way to build out the state, which will be used to determine which resources are being processed
    # and then return it instead of returning the individual pieces.
    if not has_current_state:
        print('NO STATE RECEIVED.')
        for resource in resources:
            new_state = {}

            # Primary key handling.
            if isinstance(resource['primary_key'], list):
                for key in resource['primary_key']:
                    new_state[key] = ""
            else:
                new_state[resource['primary_key']] = ""

            # Incremental key handling.
            if isinstance(resource['incremental_key'], list):
                for key in resource['incremental_key']:
                    new_state[key] = ""
            else:
                new_state[resource['incremental_key']] = ""
            new_state.update({"page_bookmark": "", "page_count": 0, "is_active_resource": False, "is_historical_sync": True})
            current_state[resource['name']] = new_state
    else:
        print(f'CURRENT STATE RECEIVED: \r{current_state}')

    for resource in resources:
        resource_name = resource['name']
        resource_table_name = resource['table_name']

        # Check the state for active resource and skip inactive resources.
        # This is designed to loop through individual resources before going onto the next and pushing a record set of a certain size.
        if not has_current_state:
            current_state[resource_name]['is_active_resource'] = True
        elif next_resource and not current_state[resource_name]['is_active_resource']:
            current_state[resource_name]['is_active_resource'] = True
        elif not current_state[resource_name]['is_active_resource']:
            continue

        # Build out the control parameters for the API call.
        api_identifier = resource['api_identifier']
        top_limit = resource.get('top_limit', resource['top_limit'])
        max_limit = resource.get('max_limit', resource['max_limit'])
        page_bookmark = current_state[resource_name]['page_bookmark']
        initial_page_count = current_state[resource_name]['page_count']
        has_more = False
        has_more_events = False

        # Build out the keys that are used for filters.
        if isinstance(resource['primary_key'], list):
            primary_key = ','.join(resource['primary_key'])
            is_primary_key_list = True
        else:
            primary_key = resource['primary_key']
            is_primary_key_list = False
        if isinstance(resource['incremental_key'], list):
            incremental_key = ','.join(resource['incremental_key'])
            is_incremental_key_list = True
        else:
            incremental_key = resource['incremental_key']
            is_incremental_key_list = False

        # The configuration can pass additional sub-resources and select criteria.
        expands = resource.get('expands', [])
        selects = resource.get('selects', [])
        filters = resource.get('additional_filters', [])

        select_param = f"&$select={','.join([field for field in selects])}" if len(selects) > 0 else ''
        expand_param = f"&$expand={','.join(expands)}" if len(expands) > 0 else ''
        filter_param = f"&$filter={' and '.join(filters)}" if filters else ''

        # Combine the API elements to build the API call.
        if resource_name == 'Tenants':
            api_identifier = f'{api_identifier}{expand_param}{select_param}{filter_param}'
            api_identifier = api_identifier.replace('{locationid}', str(config.tenantid))
        else:
            api_identifier = f'{api_identifier}{expand_param}{select_param}'

        # Determine if the resource needs historical or incremental data.
        resource_results = {"records": []}
        if current_state[resource_name]['is_historical_sync'] and not resource.get('skip_historical_resync', False):
            print(f"STARTING HISTORICAL SYNC FOR: {resource_name} - STARTING AT PAGE: {initial_page_count}")
            top_query = f"&$top={top_limit}"
            orderby_query = f"&$orderby={primary_key} asc"
            base_api_uri = f"{api_identifier}{orderby_query}{top_query}"
        else:
            print(f"STARTING INCREMENTAL SYNC FOR: {resource_name} - STARTING AT PAGE: {initial_page_count}")

            # Get the incremental value and top limit.
            incremental_value = current_state[resource_name][incremental_key]
            top_query = f"&$top={top_limit}"

            # Use the incremental value on a new incremental sync to set the starting point for all pages.
            if page_bookmark == '' and initial_page_count == 0:
                page_bookmark = incremental_value

            # Build out the rest of the odata filters and options for the api request.
            skip_incremental_flag = resource.get('skip_incremental_filter', False)
            if resource['incremental_filter_type'] in ('date', 'datetime') and not skip_incremental_flag:
                # For dates, we use greater than or equal to and timestamps, we use use greater than.
                filter_operator = 'ge' if resource['incremental_filter_type'] == 'date' else 'gt'

                # Get the timestamp from the epoch integer.
                incremental_timestamp = epoch_to_timestamp(page_bookmark)

                # Build the filter query.
                filter_query = f"&$filter={incremental_key} {filter_operator} datetime'{incremental_timestamp}'"
                for additional_filter in resource['additional_filters']:
                    today_timestamp = datetime.now().replace(hour=0, minute=0, second=0, tzinfo=timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')
                    filter_query = f"{filter_query} {additional_filter.format(today_timestamp)}"
                orderby_query = f"&$orderby={incremental_key}"
            elif resource['incremental_filter_type'] in ('int') and not skip_incremental_flag:
                filter_operator = 'gt'
                filter_query = f"&$filter={incremental_key} {filter_operator} {page_bookmark}"
                orderby_query = f"&$orderby={incremental_key}"
            else:
                filter_query = ''
                orderby_query = ''

            if resource.get('source_events_tracking', False):
                events_tracking_id = resource.get('source_events_tracking_id', '')
                events_table = resource.get('source_events_table', '')
                customer_key = config.customer_key

                # Check and set `updated_at`
                if current_state and isinstance(current_state, dict):
                    last_primary_key = current_state.get(resource_name, {}).get('last_source_events_primary_key')
                    last_updated_at = current_state.get(resource_name, {}).get('last_source_events_updated_at')

                    if not last_updated_at:
                        # No state, fallback to 1 hour earlier
                        print("No updated_at received. Using fallback to 1 hr earlier.")
                        last_primary_key = ''
                        last_updated_at = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
                else:
                    # If `current_state` is None or invalid, use fallback
                    print("No state received. fallback to 1 hr earlier.")
                    last_primary_key = ''
                    last_updated_at = (datetime.now(timezone.utc) - timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

                print(f"Event Parameters - Last Primary Key: {last_primary_key} | Last Updated At: {last_updated_at}")
                event_list, has_more_events = get_source_data_events(events_tracking_id, events_table, last_primary_key, last_updated_at, customer_key)
                
                # Create a list of all source events IDs
                list_of_ids = [event['primary_key'] for event in event_list]
                
                # Update the filter only if there are IDs to add
                if len(event_list) > 0:
                    # Update the filter to include the Loan IDs
                    id_filter = " or ({})".format("or".join([f"(id eq {entry['primary_key']})" for entry in event_list]))
                    filter_query = f"{filter_query}{id_filter}"

                    # Set state for event tracking.
                    current_state[resource_name]['last_source_events_primary_key'] = event_list[-1].get('primary_key')
                    current_state[resource_name]['last_source_events_updated_at'] = event_list[-1].get('updated_at')

            # Set the full API URI.
            base_api_uri = f"{api_identifier}{filter_query}{orderby_query}{top_query}"
        
        # Process found results until we reach the max limits.
        page_count = initial_page_count
        while True:
            # Determine skip count based on the current page.
            skip_count = top_limit * page_count
            if skip_count > 0:
                skip = f"&$skip={skip_count}"
            else:
                skip = ''
            api_uri = f"{base_api_uri}{skip}"

            # Get data from the api until no more results or reaching the max limit.
            result, has_more = fetch_data(repository, config, api_uri, expected_length=top_limit)
            resource_results['records'].extend(result)

            # Test to see if the max limit is reached or no more data for this resource, and stop processing results.
            if len(resource_results['records']) >= max_limit or not has_more:
                break
            else:
                page_count = page_count + 1
                    
        # source event result list 
        source_events_data = {"records": []}
        
        # With the while loop finished, we need the state to have the latest offsets.
        if len(resource_results['records']) > 0:
            # The resource_results contains Events Tracking data + Incremental Data based on the resources defined as part of the pipeline config. 
            # We need to re-arrange the data to make sure that all the Events Tracking data is in the front of the list, 
            # so that our logic to get the last offset works correctly   
            
            if not current_state[resource_name]['is_historical_sync'] and not resource.get('skip_historical_resync', False) and resource.get('source_events_tracking', False): 
                # append results in different list
                for entry in resource_results['records']:
                    if entry['id'] in list_of_ids:
                        resource_results['records'].remove(entry)
                        source_events_data['records'].append(entry)
                
                resource_results['records'] = source_events_data['records'] + resource_results['records']
            
            if is_primary_key_list:
                for key in resource['primary_key']:
                    current_state[resource_name][key] = resource_results['records'][-1][key]
            else:
                current_state[resource_name][resource['primary_key']] = resource_results['records'][-1][resource['primary_key']]
            if is_incremental_key_list:
                for key in resource['incremental_key']:
                    if resource['incremental_filter_type'] in ('date', 'datetime') and resource_results['records'][-1][key] == '/Date(0)/':
                        current_state[resource_name][resource['incremental_key']] = page_bookmark
                    else:
                        current_state[resource_name][key] = resource_results['records'][-1][key]
            else:
                # Only update incremental date keys if the value is not epoch 0.
                if resource['incremental_filter_type'] in ('date', 'datetime') and resource_results['records'][-1][resource['incremental_key']] == '/Date(0)/':
                    current_state[resource_name][resource['incremental_key']] = page_bookmark
                else:
                    current_state[resource_name][resource['incremental_key']] = resource_results['records'][-1][resource['incremental_key']]

        # Write records to s3 for audit trail.
        if resource_table_name not in data:
            data[resource_table_name] = {'records': []}
        data[resource_table_name]['records'].extend(resource_results['records'])
        json_str = bytes(json.dumps(data[resource_table_name]['records']).encode("utf-8"))
        push_data_s3(resource_table_name, config, json_str, current_ts)

        # Determine next steps for this resource based on the results of the api call.
        if has_more:
            # We have more data for this resource but we've reached the max limit. Set page and return to caller.
            current_state[resource_name]['page_bookmark'] = page_bookmark
            current_state[resource_name]['page_count'] = page_count + 1
        elif has_more_events:
            has_more = True
        else:
            # We have reached the end of this resource. Reset and move forward.
            next_resource = True
            current_state[resource_name]['is_historical_sync'] = False
            current_state[resource_name]['is_active_resource'] = False
            current_state[resource_name]['page_bookmark'] = ''
            current_state[resource_name]['page_count'] = 0
            if resource_name == last_resource_name:
                current_state[first_resource_name]['is_active_resource'] = True

    return data, has_more, current_state
