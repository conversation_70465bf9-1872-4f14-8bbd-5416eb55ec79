import json
import time
import re
from datetime import datetime, timezone, time, timedelta, timezone
from s3_utils import push_data_s3, list_s3_files, read_file
from fivetran_utils import get_hash_key


def fetch_data(repository, token, resource_id, page_number):
    """
    Fetches data from an API using a specified repository object's method.
    Handles pagination and rate limiting based on API response headers.

    This function calls the API using the repository's 'get' method and manages
    pagination if more than 30 results are available. It also checks for and
    respects rate limiting from the API, pausing execution if necessary.

    Args:
        repository (object): The repository object which has a 'get' method for API calls.
        token (str): Authorization token used for API requests in the header.
        resource_id (str): The api_url identifier for the resource to fetch data from.
        page_number (int): The current page number to fetch data from.

    Returns:
        tuple: A tuple containing:
            - list: A list of results fetched from the API.
            - bool: A boolean flag indicating if there are more pages to fetch.
            - int: The current page number after the fetch.

    Notes:
    - The function prints messages about the process, including any rate limits reached and the number of records fetched.
    - It assumes the response from 'get' method is a tuple containing results at index 0 and headers at index 1.
    - It checks for 'TotalResultsAvailable' in results to determine pagination and rate limiting headers.
    """

    custom_headers = {'AuthToken': token}
    try:
        # Call the create method from repository object
        retrieved_resource = repository.get(resource_id, headers=custom_headers)
        if retrieved_resource is None:
            print(f"No data fetched for {resource_id}.")
            return [], False, page_number
        else:
            # Handle the results and headers.
            results = retrieved_resource[0]
            headers = retrieved_resource[1]
            if results["TotalResultsAvailable"] > 30:
                current_page = page_number
                # Extracting the last page number from the fetched results
                last_page = int(re.search(r'page=(\d+)', results["Links"][-1]['HRef']).group(1))
                print(f"Fetched {len(results['Results'])} records on page_number {current_page} out of a total of {last_page} pages for {resource_id}")

                if current_page < last_page:
                    has_more_flag = True
                else:
                    has_more_flag = False

                # Handle rate limiting.
                if int(headers.get('x-ratelimit-remaining', 0)) == 0:
                    print("Rate limit reached, pausing sync until next run.")
                    has_more_flag = False
                elif int(headers.get('x-ratelimit-remaining', 0)) <= 5:
                    print(f"Remaining requests => {headers.get('x-ratelimit-remaining', 0)}.")
                    print('Reset time is in 5 seconds. Waiting.')
                    time.sleep(5)

            else:
                has_more_flag = False
                current_page = 1

            return results['Results'], has_more_flag, current_page
    except Exception as e:
        print(f"Failed to fetch data: {e}")
        raise


def get_resources_data(repository, config, current_ts):
    """
    Processes a list of resources from a configuration object, fetching data for each resource via an API,
    and manages the state of data processing. It handles pagination and determines which resources are
    active for processing based on their state. If a resource does not have a primary key, it generates
    a hash key for each record.

    Args:
        repository (object): The repository object to interact with the API.
        config (object): Configuration object containing API keys, resource definitions, and state.
        current_ts (datetime): Current timestamp used for logging and time-based operations.

    Returns:
        tuple:
            data (dict): A dictionary containing the fetched data organized by table names.
            has_more (bool): A boolean indicating if there are more pages of data to be fetched.
            current_state (dict): The updated state of each resource after processing.

    """
    data = {}
    current_state = config.current_state
    resources = config.resources
    has_current_state = len(current_state) > 0
    next_resource = False
    first_resource_name = resources[0]['name']
    last_resource_name = resources[-1]['name']

    # Build out a dictionary of current state to return if not passed.
    # This is a way to build out the state, which will be used to determine which resources are being processed
    # and then return it instead of returning the individual pieces.
    if not has_current_state:
        print('NO STATE RECEIVED.')
        for resource in resources:
            new_state = {}

            # Primary key handling.
            if isinstance(resource['primary_key'], list):
                for key in resource['primary_key']:
                    new_state[key] = ""
            else:
                new_state[resource['primary_key']] = ""

            # Incremental key handling.
            if isinstance(resource['incremental_key'], list):
                for key in resource['incremental_key']:
                    new_state[key] = ""
            else:
                new_state[resource['incremental_key']] = ""

            # For historical file sync, determine the earliest file's last modified date from S3
            # and use it as the starting point for processing.
            if resource['historical_file_sync']:
                try:
                    oldest_historical_file = min(list_s3_files(config.s3_bucket, f"{config.s3_key_prefix}{resource['table_name']}_{config.s3_filename_identifier}_"), key=lambda file: file[1])
                    if oldest_historical_file:
                        _, last_modified = oldest_historical_file
                        new_state["last_processed_file_timestamp"] = last_modified.replace(hour=0, minute=0, second=0, microsecond=0)
                except ValueError:
                    print('No historical files to sync.')
                    new_state["last_processed_file_timestamp"] = current_ts.astimezone(timezone.utc).isoformat()

            new_state.update({"page_bookmark": 1, "page_count": 0, "is_active_resource": False, "is_historical_sync": True})

            current_state[resource['name']] = new_state
    else:
        print(f'CURRENT STATE RECEIVED: \r{current_state}')

    # Step 1 : Iterate over all the resources passed by Fivetran
    for resource in resources:
        resource_name = resource['name']
        resource_table_name = resource['table_name']

        # This section ensures that only active resources are processed.
        # It handles both initialization and subsequent checks for resource activity.
        if not has_current_state:
            # If there is no existing state data when the process starts, it implies that all resources might need to be processed.
            # This block will be entered on the first run when no state has been established yet.
            # Here, we assume the resource needs to be processed and thus set it as active.
            current_state[resource_name]['is_active_resource'] = True
        elif next_resource and not current_state[resource_name]['is_active_resource']:
            # If it's a subsequent iteration and the 'next_resource' flag is set (indicating a move to another resource),
            # but the current resource is found inactive in the state, then this block won't execute for an active first resource.
            # This is primarily for reactivating resources marked inactive in previous runs.
            current_state[resource_name]['is_active_resource'] = True
        elif not current_state[resource_name]['is_active_resource']:
            # If the current resource is inactive, we skip it. This would not execute for an active first resource
            # because the condition specifically checks for inactivity.
            continue

        # If the first resource is active and there is existing state data indicating it as such,
        # none of the specific conditions to change its active status or to skip it would apply.
        # The resource would just be processed as normal because it does not meet any of the conditions
        # that lead to setting 'is_active_resource' or triggering 'continue'.

        if resource['historical_file_sync'] and current_state[resource_name]['is_historical_sync']:
            # This block handles the historic load if the API doesn't give us a way to get the historical data.
            # In this block, we fetch the files from S3 and read the content of the JSON files.
            has_more = False
            start_time = current_state[resource_name]['last_processed_file_timestamp']
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time).astimezone(timezone.utc)
            end_time = start_time + timedelta(days=int(resource['historical_sync_window_in_days']))

            # Fetch historical files within a defined time window (e.g., 2 days)
            # to enable date-wise tracking and avoid memory or timeout issues.
            # Read content of JSON files
            result_list = []
            for file in list_s3_files(config.s3_bucket, f"{config.s3_key_prefix}{resource['table_name']}_{config.s3_filename_identifier}_", start_time=start_time, end_time=end_time):
                try:
                    file_result_list = []
                    file_data = read_file(config.s3_bucket, file[0], format='json')
                    # Extract only the JSON content from the dictionary
                    if isinstance(file_data, dict):
                        file_result_list.extend(file_data.values())

                except Exception as e:
                    print(f"Error reading {key}: {e}")
                ### One time change to update the hash_key
                ### Everything below this line till 203 can be removed after the first run
                # Update the hash_key for each record
                # Generate a new hash_key for the record
                filename = file[0].split('.')[0].split('/')[-1]
                timestamp_str = filename.rsplit('_', 2)[-2] + filename.rsplit('_', 2)[-1]
                record_synched = datetime.strptime(timestamp_str, "%d%m%Y%H%M%S").replace(tzinfo=timezone.utc).isoformat()
                for record in file_result_list:
                    record.pop("gestalt_primary_key", None)  # Remove the old hash_key if it exists
                    record.pop("record_synched", None)  # Remove the old record_synched if it exists
                    record['_gestalt_synced'] = record_synched
                    record["_gestalt_primary_key"] = get_hash_key(record)

                result_list.extend(file_result_list)
                # Updating the file that was rewritten with updated keys
                json_str = bytes(json.dumps(file_result_list).encode("utf-8"))
                push_data_s3(resource_table_name, config, json_str, datetime.strptime(timestamp_str, "%d%m%Y%H%M%S"), resource['historical_file_sync'])

            if end_time >= datetime.now(timezone.utc):
                # If the end time is greater than the current time, it means we have reached the end of the historical sync
                has_more = False
            else:
                has_more = True

            data[resource_table_name] = {"records": result_list}

        else:
            # Initializing the parameters needed to call the API.
            resource_result = []
            api_identifier = f"{resource['api_identifier']}"
            top_limit = resource.get('top_limit', resource['top_limit'])
            max_limit = resource.get('max_limit', resource['max_limit'])
            page_bookmark = current_state[resource_name]['page_bookmark']
            initial_page_count = current_state[resource_name]['page_count']
            has_more = False

            # Initializing the page_number in case if last execution did not process all pages
            page_number = page_bookmark + 1 if initial_page_count > 0 else 1

            # If API has records in multiple pages, you can loop through the pages and fetch the records
            num_pages_processed = 0
            while True:
                # Fetch data using the API.
                additional_filter_query = ""
                for additional_filter in resource['additional_filters']:
                    additional_filter_query += additional_filter
                base_api_uri = f'{api_identifier}{additional_filter_query.format(page_number = page_number, page_size = top_limit)}'
                # base_api_uri = f'{api_identifier}?page={page_number}&pageSize={top_limit}&{additional_filter_query}'
                # base_api_uri = api_identifier.format(page_number = page_number, page_size = top_limit)
                table_data, has_more, processed_page_number = fetch_data(repository, config.defilos_api_key, base_api_uri, page_number)

                # Append the fetched data to the result list.
                resource_result.extend(table_data)
                num_pages_processed += 1

                # Check if there is more data to fetch.
                if not has_more or num_pages_processed >= max_limit:
                    break

                # Increment the page number for the next API call.
                page_number = processed_page_number + 1

            # Generate hash of record if primary key is absent
            if resource['primary_key'] == "_gestalt_primary_key":
                resource_result = [{**record, "_gestalt_synced": datetime.now(timezone.utc).replace(microsecond=0).isoformat()} for record in resource_result]
                resource_result = [{**record, "_gestalt_primary_key": get_hash_key(record)} for record in resource_result]

            # Handle the results.
            data[resource_table_name] = {"records": resource_result}

            # Auditing trail for data.
            json_str = bytes(json.dumps(resource_result).encode("utf-8"))
            push_data_s3(resource_table_name, config, json_str, current_ts)

        # Determine next steps for this resource based on the results of the api call.
        if has_more and current_state[resource_name]['is_historical_sync']:
            # We have more data for this resource but we've reached the max limit. Set page and return to caller.
            current_state[resource_name]['last_processed_file_timestamp'] = str(end_time)
            # current_state[resource_name]['is_historical_sync'] = True
        elif has_more:
            # We have more data for this resource but we've reached the max limit. Set page and return to caller.
            current_state[resource_name]['page_bookmark'] = processed_page_number
            current_state[resource_name]['page_count'] = num_pages_processed
            # current_state[resource_name]['is_historical_sync'] = False
        else:
            # We have reached the end of this resource. Reset and move forward.
            next_resource = True
            current_state[resource_name]['is_historical_sync'] = False
            current_state[resource_name]['is_active_resource'] = False
            current_state[resource_name]['page_bookmark'] = ''
            current_state[resource_name]['page_count'] = 0
            if resource_name == last_resource_name:
                current_state[first_resource_name]['is_active_resource'] = True

    return data, has_more, current_state
