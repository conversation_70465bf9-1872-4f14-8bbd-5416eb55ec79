import os
from snowflake.snowpark import Session
from contextlib import contextmanager
from ssm_utils import download_key_from_ssm

# Get credentials from AWS Parameter Store
@contextmanager
def snowflake_session(customer_key):
    """Context manager for Snowflake session using Snowpark."""
    # Define Snowflake connection configuration
    snowpark_config = {
        "account": download_key_from_ssm("/gestalt/client/snowflake/prod/credentials/account"),
        "user": f"{customer_key.upper()}_USER",
        "password": download_key_from_ssm(f"/{customer_key.replace('_','-').lower()}/snowflake/prod/credentials/account-core/password"),
        "role": f"{customer_key.upper()}_ROLE",
        "warehouse": f"{customer_key.upper()}_WH_XSMALL",
        "database": f"{customer_key.lower()}_db",
        "schema": "grand_vault"
    }

    # Establish the Snowpark session
    session = Session.builder.configs(snowpark_config).create()

    try:
        yield session
    finally:
        session.close()

def execute_query(query, customer_key, params=None):
    """Execute a query and return results using Snowpark."""
    with snowflake_session(customer_key) as session:
        # Run query
        if params:
            result = session.sql(query.format(*params)).collect()
        else:
            result = session.sql(query).collect()
        return result