import boto3

# Initialize the SSM clients
ssm_client = boto3.client('ssm', region_name='us-east-2')

def download_key_from_ssm(parameter_name):
    try:
        # Retrieve the encrypted private key from Parameter Store
        response = ssm_client.get_parameter(
            Name=parameter_name,
            WithDecryption=True
        )
        parameter_value = response['Parameter']['Value']
        return parameter_value
    except Exception as e:
        print(f"Error: {e}")
        return None