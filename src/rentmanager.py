from rest_api_repository import RestAPIRepository
from s3_utils import push_data_s3
import math
import json
import time
from datetime import datetime, timezone, timedelta


def authenticate(repository, username, password):
    custom_headers = {'Content-Type': 'application/json'}
    data_to_create = {'UserName': username, 'Password': password}
    # Do a login, with retry built in for rate limiting.
    for i in range(10):
        created_resource, header = repository.create(data_to_create, "Authentication/Authorizeuser", headers=custom_headers)
        if isinstance(created_resource, dict):
            print(f"Rate limiting detected. Delaying 15 seconds. Delay instance {i + 1} of 10.")
            # Wait for 15 seconds before repeating the loop
            time.sleep(15)
        elif isinstance(created_resource, str):
            token = created_resource.strip('"')
            return token

    raise ConnectionError('Could not authenticate.')

def fetch_data(repository, token, resource_id, location_id):
    custom_headers = {'x-rm12api-apitoken': token, 'X-RM12Api-LocationID': location_id}
    try:
        retrieved_resource = repository.get(resource_id, headers=custom_headers)
        if retrieved_resource is None:
            print(f"No data fetched for {resource_id}.")
            return [], False
        else:
            # Handle the results and headers.
            results = retrieved_resource[0]
            headers = retrieved_resource[1]
            total_results = int(headers.get('X-Total-Results', 0))
            print(f"Fetched {len(results)} records out of a total of {total_results} for {resource_id}")
            if len(results) < total_results:
                hasMore_flag = True
            else:
                hasMore_flag = False

            # Handle rate limiting.
            if int(headers.get('x-ratelimit-remaining', 0)) == 0:
                print("Rate limit reached, pausing sync until next run.")
                hasMore_flag = False
            elif int(headers.get('x-ratelimit-remaining', 0)) <= 50:
                print(f"Remaining requests => {headers.get('x-ratelimit-remaining', 0)}.")
                reset_timestamp = int(headers.get('x-ratelimit-reset', 0))
                current_timestamp = int(time.time())
                delta = timedelta(seconds=reset_timestamp - current_timestamp)
                print(f"Reset time is in {int(delta.total_seconds())} seconds. Waiting.")
                time.sleep(int(delta.total_seconds()))

            return results, hasMore_flag
    except Exception as e:
        print(f"Failed to fetch data: {e}")
        raise


def get_resources_data(repository, config, current_ts, page_number=1):
    current_state = config.current_state
    print(f'Current STATE Received :\r{current_state}')
    resources = config.resources

    # Initialize STATES for all the resources being passed via 'resources'
    for resource in resources:
        if resource['table_name'] not in current_state:
            print(f"{resource['table_name']} NOT Found in STATE")
            if resource['incremental_key'] == 'UpdateDate':
                current_state[resource['table_name']] = "1970-01-01 00:00:00"
            else:
                current_state[resource['table_name']] = "0"

    # STATE initialized
    print(f'UPDATED STATE :\r{current_state}')

    username = config.rm_username
    password = config.rm_password
    data = {}
    result_size = 0
    global_has_more_flag = False

    # Step 1 : Authenticate and get Token
    if current_state.get('token', '') != '':
        token = current_state.get('token', '')
        # Test to make sure the token is valid.
        fetch_data(repository, token, 'Users', config.rm_locationid)
        if repository.get_last_status_code() == '' or repository.get_last_status_code() not in (200, 204):
            token = authenticate(repository, username, password)
            print('Retrieving new authentication token.')
        else:
            print('Using existing authentication token.')
    else:
        token = authenticate(repository, username, password)
        print('Retrieving new authentication token.')

    current_state['token'] = token

    # Step 2 : Iterate over all the resources passed by Fivetran
    for resource in resources:
        table_name = resource['table_name']

        # This is to support linked sub-resources to the main resource for example Loan => ["LoanRates", "LoanTransactions"]
        subresources = resource.get('subresources', [])

        # Check if the resource supports filtering/ Ordering, if not then have to rely on PAGE by PAGE data fetch and incremental wont be possible
        if 'NoFilteringSupport' in resource:
            resource_id = f"{table_name}?pagesize={config.page_size}&pagenumber={page_number}"
        else:
            filter_query = f"filterExpression={resource['incremental_key']},ge,{current_state[table_name]}"
            additional_filters = resource.get('additional_filters', '')
            for additional_filter in additional_filters:
                filter_query = f"{filter_query}{additional_filter.format(current_state[table_name])}"
            resource_id = f"{table_name}/Search?OrderingOptions={resource['incremental_key']}&{filter_query}&pagesize={config.page_size}&pagenumber={page_number}"

        if subresources:
            # Convert list to comma-separated string
            comma_separated_string = ','.join(map(str, subresources))
            subresources_string = f"&embeds={comma_separated_string}"
            resource_id = resource_id + subresources_string

        # Call the REST API.
        table_data, has_more_flag = fetch_data(repository, token, resource_id, config.rm_locationid)

        # Flag for more data.
        if has_more_flag:
            global_has_more_flag = True

        # Handle the results.
        data[table_name] = {"records": table_data}
        if len(table_data) > 0:
            # Build out the current total result size.
            result_size = result_size + len(table_data)
            print(f"Total Rolling Results Retrieved => {result_size}")

            # With the loop finished, we need the state to have the latest offsets.
            current_state[table_name] = data[table_name]['records'][-1][resource['incremental_key']]

            # Auditing trail for data.
            json_str = bytes(json.dumps(table_data).encode("utf-8"))
            push_data_s3(table_name, config, json_str, current_ts)

    return data, global_has_more_flag, current_state
