import requests
from config import Config


class RestAPIRepository:
    def __init__(self, config):
        self.base_url = config.api_base_url
        self._last_status_code = ''

    def _handle_response(self, response):
        content_type = response.headers.get("Content-Type", "")
        if "application/json" in content_type:
            return response.json(), response.headers
        else:
            return response.text, response.headers

    def get_last_status_code(self):
        return self._last_status_code

    def get(self, resource_id, headers=None):
        response = requests.get(f"{self.base_url}/{resource_id}", headers=headers, timeout=300)
        self._last_status_code = response.status_code
        if response.status_code == 200:
            return self._handle_response(response)
        else:
            print(f"GET on Resource {resource_id} failed with response code = {response.status_code}")
            return None

    def create(self, data, resource_id=None, headers=None):
        if resource_id is None:
            response = requests.post(self.base_url, json=data, headers=headers, timeout=300)
        else:
            response = requests.post(f"{self.base_url}/{resource_id}", json=data, headers=headers, timeout=300)
        if response.status_code in (200, 401):
            return self._handle_response(response)
        else:
            print(f"Error: Code returned {response.status_code}")
            raise ConnectionError("CREATE failed")

    def update(self, resource_id, data, headers=None):
        response = requests.put(f"{self.base_url}/{resource_id}", json=data, headers=headers, timeout=300)
        return response.json()

    def delete(self, resource_id, headers=None):
        response = requests.delete(f"{self.base_url}/{resource_id}", headers=headers, timeout=300)
        return response.status_code == 204
