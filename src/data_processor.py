import hashlib
import json

class DataProcessor:
    def __init__(self):
        pass

    def process_data(self, data, table_name, p_key_field):
        processed_data = []
        salt = self._generate_table_salt(table_name)
        for record in data:
            # Transform the primary key
            transformed_record = self._transform_primary_key(record, p_key_field, salt)
            processed_data.append(transformed_record)
        return processed_data

    def _generate_table_salt(self, table_name):
        return table_name.encode()

    def _transform_primary_key(self, record, p_key_field, salt):
        original_p_key = record.get(p_key_field)
        transformed_p_key = self._generate_hash_key(original_p_key, salt)
        transformed_record = record.copy()
        transformed_record[p_key_field] = transformed_p_key
        transformed_record['metadata'] = {'original_p_key': original_p_key}
        return transformed_record

    def _generate_hash_key(self, p_key, salt):
        hash_input = f"{p_key}{salt}".encode()
        return hashlib.sha256(hash_input).hexdigest()
