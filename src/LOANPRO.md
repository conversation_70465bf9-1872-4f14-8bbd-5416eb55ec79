# Loanpro API Documentation

## Overview
The Loanpro API provides programmatic access to retrieve data from the database. The API is RESTful, and it uses JSON for request and response bodies.

## Endpoints
The following endpoints are integrated with in the API:

* `Customers`
    - **Historical:** `GET /Customers?nopaging&$expand=Loans,Phones,CreditScore,PrimaryAddress,MailAddress&$select=*,Loans/id,Phones/*,CreditScore/*,PrimaryAddress/*,MailAddress/*&$orderby=id asc&$top=1000` - Retrieves all Customers and nested collections from the database. 1000 records takes about 55 seconds.
    run at 1000
    - **Incremental:** `GET /Customers?nopaging&$expand=Loans,Phones,CreditScore,PrimaryAddress,MailAddress&$select=*,Loans/id,Phones/*,CreditScore/*,PrimaryAddress/*,MailAddress/*&$top=1000&$filter=lastUpdate gt datetime'{date}'` - Retrieves Customers by last update. 1000 records takes about 3 seconds.

* `Loans`
    - **Historical:** `GET /Loans?nopaging&$expand=LoanSettings,LoanSetup,Charges,Credits,Portfolios&$select=*,LoanSettings/*,LoanSetup/*,Charges/*,Credits/*,Portfolios/*&$orderby=id asc&$top=1000` - Retrieves all Loans and nested collections from the database. 1000 records takes about 20 seconds.
    run at 3000
    - **Incremental:** `GET /Loans?nopaging&$expand=LoanSettings,LoanSetup,Charges,Credits&$top=1000&$filter=lastMaintRun gt datetime'2024-06-26'` - Retrieves all Loans and nested collections from the database. 1000 records takes about 30 seconds.

* `LoanTransactions`
    - **Historical:** `GET /LoanTransactions?nopaging&$orderby=id asc&$top=20000`: Retrieves Loan Transactions by date. 20000 records takes about 18 seconds.
    run at 60000
    - **Incremental:** `GET /LoanTransactions?nopaging&$orderby=id desc&$filter=id gt {$last_id}&$top=20000`: Retrieves Loan Transactions by date. 20000 records takes about 18 seconds.

* `LoanStatusArchive`
    - **Historical:** `GET /LoanStatusArchive?nopaging&$orderby=id asc&$top=10000`: Retrieves Loan Status Archive data by ID. 10000 records takes about 20 seconds.
    run at 30000
    - **Incremental:** `GET /LoanStatusArchive?nopaging&$orderby=id desc&$filter=id gt {last_id}&$top=10000`: Retrieves Loan Status Archive data by ID. 10000 records takes about 20 seconds.

* `Collateral`
    - **Historical:** `GET /Collateral?nopaging&$expand=CustomFieldValues&$orderby=id asc&$top=5000`: Retrieves Collateral data by ID. 5000 records takes about 20 seconds.
    run at 15000
    - **Incremental:** `GET /Collateral?nopaging&$expand=CustomFieldValues&$orderby=id desc&$filter=id gt {last_id}&$top=5000`: Retrieves Collateral data by ID. 5000 records takes about 10 seconds.

* `Payments`
    - **Historical:** `GET /Payments?nopaging&$orderby=id asc&$top=3000`: Retrieves Payments data by ID. 3000 records takes about 20 seconds.
    run at 9000
    - **Incremental:** `GET /Payments?nopaging&$orderby=id desc&$filter=id gt {last_id}&$top=3000`: Retrieves Payments data by ID. 3000 records takes about 10 seconds.
* `LoanStatus`
    - **Historical:** `GET /LoanStatus?nopaging&$expand=&$orderby=id asc&$top=6`: There are only 6 records present for this table taking .4s (it's a mapping table).
    - **Incremental:** `GET /LoanStatus?nopaging&$expand=&$orderby=id desc&$filter=id gt {last_id}&$top=6` :Retrieves LoanStatus data by ID. 6 records takes about .4 Seconds.
* `AutoPays`
    - **Historical:** `GET /AutoPays?nopaging&$orderby=id asc&$top=4000`: Retrieves Autopays data by ID. 4000 records takes about 20 seconds.
    run at 12000
    - **Incremental:** `GET /AutoPays?nopaging&$orderby=id desc&$filter=id gt {last_id}&$top=4000`: Retrieves Autopays data by ID. 4000 records takes about 10 seconds.

## Authentication
The API uses access tokens which are obtained from the customer. Create a bearer token and tenant ID by authenticating with a client ID and secret.

