import json


class Config:
    def __init__(self, event):
        # General & Fivetran specific options.
        self.setup_test = event.get('setup_test', False)
        self.current_state = event.get('state', {})
        self.log_level = 'ERROR'  # Default log level, can be adjusted if needed
        self.page_size =  1000  # Default page size, can be adjusted if needed
        self.max_results = 10000  # Default result size, can be adjusted if needed

        # Custom payload options.
        self.api_base_url = event['customPayload'].get('api_base_url' ,'')
        self.s3_bucket = event['customPayload'].get('s3_bucket', '')
        self.s3_key_prefix = event['customPayload'].get('s3_key', '')
        self.s3_filename_identifier = event['customPayload'].get('s3_filename_identifier', '')
        
        # Customer Specific options from customPayload.
        self.customer_key = event['customPayload'].get('customer_key', '')
        
        # Module options, with the current options being: 'loanpro', 'rentmanager'.
        self.partner_key = event['customPayload'].get('partner_key', '')
        
        # Rentmanager options.
        self.rm_username = event['secrets'].get('username', '')
        self.rm_password = event['secrets'].get('password', '')
        self.rm_locationid = event['customPayload'].get('locationid', '') # Metra = 8 , Crow = 15

        # Loanpro options.
        self.lp_bearer = event['secrets'].get('bearer', '')
        self.tenantid = event['secrets'].get('tenantid', '')

        # Defilos options.
        self.defilos_api_key = event['secrets'].get('api-key', '')

        # Extract and parse 'resources'. Fivetran sends it as a JSON String.
        resources_str = event['customPayload'].get('resources', '')
        resources = json.loads(resources_str)
        self.resources = check_keys(resources)

        # Sync Through S3 options.
        self.sync_through_s3_bucket=event.get('bucket', '')
        self.sync_through_s3_key=event.get('file', '')

    def print_config(self):
        # Remove any sensitve variables from being printed.
        sensitive_keys = ['rm_password', 'lp_bearer']
        print_keys = self.__dict__.copy()
        for key in sensitive_keys:
            print_keys.pop(key, None)

        # Get all class variables sorted.
        variables_str = '\n'.join(f"{str(key)}: {str(value)}" for key, value in sorted(print_keys.items()))
        # Remove newlines and replace with carriage returns to preserve formatting and print within a single cloudwatch entry.
        variables_str = variables_str.replace('\n', '\r')
        print(f"CONFIGURATION ATTRIBUTES: \r{variables_str}")

# Instantiate the configuration within the Lambda handler
def get_config(event):
    return Config(event)

def check_keys(resources):
    """
    Ensures that each resource dictionary in the provided list has the 'primary_key' and 
    'incremental_key' keys. If these keys are missing, they are set to default values "_gestalt_primary_key" for 
    'primary_key' and "_gestalt_incremental_key" for 'incremental_key'

    Parameters:
        resources (list of dict): A list of dictionaries representing resources. 

    Returns:
        list of dict: The same list of dictionaries after ensuring that each dictionary has 
                      'primary_key' and 'incremental_key' keys set.

    Example:
Example:
        >>> resources = [
        ...     {
        ...         "name": "Dealers",
        ...         "table_name": "Dealers",
        ...         "expands": [],
        ...         "api_identifier": "Dealers?page={page_number}&pageSize={page_size}&withAllData=true",
        ...         "additional_filters": [],
        ...         "skip_incremental_filter": false,
        ...         "top_limit": 30,
        ...         "max_limit": 30
        ...     }
        ... ]
        >>> updated_resources = check_keys(resources)
        >>> updated_resources
        [{'name': 'Dealers', 'table_name': 'Dealers', 'expands': [], 
          'api_identifier': 'Dealers?page={page_number}&pageSize={page_size}&withAllData=true', 
          'additional_filters': [], 'skip_incremental_filter': false, 'top_limit': 30, 'max_limit': 30, 
          'primary_key': '_gestalt_primary_key', 'incremental_key': '_gestalt_incremental_key'}
        ]
    """
    for resource in resources:
        resource.setdefault('primary_key', "_gestalt_primary_key")
        resource.setdefault('incremental_key', "_gestalt_incremental_key")

    return resources
