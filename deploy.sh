#!/bin/bash

# Define the deployment package name
PACKAGE_NAME="gestalt-api-pipeline-connector.zip"

# Remove existing package
rm -f $PACKAGE_NAME

# Create a new package with the source code
cd ~/repos/gestalt-api-pipeline-connector/src
zip -r ../$PACKAGE_NAME ./*
cd ..

# Create a clean virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies in a temporary directory and add them to the package
mkdir temp_pkg
pip install --platform manylinux2014_x86_64 --only-binary=:all: -r src/requirements.txt -t temp_pkg
cd temp_pkg
zip -r ../$PACKAGE_NAME ./*
cd ..
rm -rf temp_pkg

# Deactivate and remove the virtual environment
deactivate
rm -rf venv
