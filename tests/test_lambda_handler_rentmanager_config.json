{"locationid": "8", "partner_key": "rentmanager", "s3_key": "partners/rentmanager/api_process/", "s3_filename_identifier": "metra", "s3_bucket": "customer-mvp", "api_base_url": "https://metra.api.rentmanager.com", "agent": "Fivetran AWS Lambda Connector/fivetran/aws_lambda", "setup_test": false, "resources": [{"table_name": "Assets", "primary_key": "AssetID", "incremental_key": "UpdateDate"}, {"table_name": "AccountingPeriodSeries", "primary_key": "AccountingPeriodSeriesID", "incremental_key": "AccountingPeriodSeriesID"}, {"table_name": "AssetStatus", "primary_key": "AssetStatusID", "incremental_key": "UpdateDate"}, {"table_name": "AssetStatusHistory", "primary_key": "AssetStatusHistoryID", "incremental_key": "UpdateDate"}, {"table_name": "AssetTitleStatuses", "primary_key": "AssetTitleStatusID", "incremental_key": "UpdateDate"}, {"table_name": "AssetTypes", "primary_key": "AssetTypeID", "incremental_key": "UpdateDate"}, {"table_name": "Loans", "primary_key": "LoanID", "incremental_key": "UpdateDate", "subresources": ["LoanRates"]}, {"table_name": "LoanTransactions", "primary_key": "LoanTransactionID", "incremental_key": "LoanTransactionID"}, {"table_name": "Tenants", "primary_key": "TenantID", "incremental_key": "UpdateDate", "subresources": ["Addresses", "Balance", "Contacts", "OpenBalance", "UserDefinedValues"]}, {"table_name": "Charges", "primary_key": "ChargeID", "incremental_key": "UpdateDate", "subresources": ["Allocations"]}, {"table_name": "Credits", "primary_key": "CreditID", "incremental_key": "UpdateDate", "subresources": ["Allocations"]}, {"table_name": "Payments", "primary_key": "PaymentID", "incremental_key": "UpdateDate", "additional_filters": ["|Allocations.UpdateDate,gt,{0}"], "subresources": ["Allocations", "PaymentReversal"]}, {"table_name": "ChargeTypes", "primary_key": "ChargeTypeID", "incremental_key": "ChargeTypeID"}, {"table_name": "Properties", "primary_key": "PropertyID", "incremental_key": "PropertyID"}, {"table_name": "Units", "primary_key": "UnitID", "incremental_key": "UnitID"}, {"table_name": "UnitStatusTypes", "primary_key": "UnitStatusTypeID", "NoFilteringSupport": "True", "incremental_key": "UnitStatusTypeID"}, {"table_name": "UnitTypes", "primary_key": "UnitTypeID", "incremental_key": "UnitTypeID"}]}