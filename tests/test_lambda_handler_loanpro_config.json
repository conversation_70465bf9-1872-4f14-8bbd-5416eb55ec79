{"locationid": "5202753", "partner_key": "loanpro", "s3_key": "partners/loanpro/api_process/", "s3_filename_identifier": "5202753", "s3_bucket": "customer-mvp", "api_base_url": "https://loanpro.simnang.com/api/public/api/1/odata.svc", "agent": "Fivetran AWS Lambda Connector/fivetran/aws_lambda", "setup_test": false, "customer_key": "CUSTOMER_MARINE_ONE", "resources": [{"name": "Tenants", "table_name": "Tenants", "expands": ["CompanyPhones", "CompanyEmails", "PrimaryContact", "AgentUsers"], "api_identifier": "Tenants?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": ["id eq {locationid}"], "incremental_filter_type": "int", "skip_incremental_filter": true, "top_limit": 1000, "max_limit": 1000}, {"name": "LoanStatus", "table_name": "LoanStatus", "expands": ["LoanSubStatus"], "api_identifier": "LoanStatus?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": [], "incremental_filter_type": "int", "top_limit": 1000, "max_limit": 1000}, {"name": "Autopays", "table_name": "Autopays", "api_identifier": "Autopays?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": [], "incremental_filter_type": "int", "top_limit": 4000, "max_limit": 12000}, {"name": "Loans", "table_name": "Loans", "expands": ["LoanSettings", "LoanSettings/SrcCompany", "Charges", "Credits", "Insurance", "Portfolios", "SubPortfolios", "LoanSetup"], "api_identifier": "Loans?nopaging", "primary_key": "id", "incremental_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additional_filters": ["or (created ge datetime'{0}' and lastMaintRun eq datetime'1970-01-01T00:00:00')"], "incremental_filter_type": "datetime", "top_limit": 250, "max_limit": 750, "source_events_tracking": true, "source_events_tracking_id": "loan_id", "source_events_table": "loanpro_events_loan_settings"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "table_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "expands": ["LoanSettings/CustomFieldValues", "LoanSettings/CustomFieldValues/CustomField", "LoanSetup/CustomFieldValues", "LoanSetup/CustomFieldValues/CustomField", "Insurance/CustomFieldValues", "Insurance/CustomFieldValues/CustomField", "ChecklistItemValues", "ChecklistItemValues/Checklist", "ChecklistItemValues/Checklist/ChecklistItems"], "selects": ["id", "humanActivityDate", "created", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LoanSettings/CustomFieldValues/*", "LoanSettings/CustomFieldValues/CustomField/name", "LoanSettings/CustomFieldValues/CustomField/selectOptions", "LoanSetup/CustomFieldValues/*", "LoanSetup/CustomFieldValues/CustomField/name", "LoanSetup/CustomFieldValues/CustomField/selectOptions", "Insurance/CustomFieldValues/*", "Insurance/CustomFieldValues/CustomField/name", "Insurance/CustomFieldValues/CustomField/selectOptions", "ChecklistItemValues/entityId", "ChecklistItemValues/checklistId", "ChecklistItemValues/checklistItemId", "ChecklistItemValues/checklistItemValue", "ChecklistItemValues/lastUpdated", "ChecklistItemValues/Checklist/id", "ChecklistItemValues/Checklist/title", "ChecklistItemValues/Checklist/ChecklistItems/id", "ChecklistItemValues/Checklist/ChecklistItems/title"], "api_identifier": "Loans?nopaging", "primary_key": "id", "incremental_key": "humanActivityDate", "additional_filters": ["or (created ge datetime'{0}' and lastMaintRun eq datetime'1970-01-01T00:00:00')"], "incremental_filter_type": "datetime", "top_limit": 100, "max_limit": 500, "source_events_tracking": true, "source_events_tracking_id": "loan_id", "source_events_table": "loanpro_events_loan_settings"}, {"name": "LoanCustomers", "table_name": "LoanCustomers", "expands": ["Customers"], "selects": ["id", "humanActivityDate", "created", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Customers/id", "Customers/customId", "Customers/mcId", "Customers/created", "Customers/lastUpdate", "Customers/loanRole"], "api_identifier": "Loans?nopaging", "primary_key": "id", "incremental_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additional_filters": ["or (created ge datetime'{0}' and lastMaintRun eq datetime'1970-01-01T00:00:00')"], "incremental_filter_type": "datetime", "top_limit": 700, "max_limit": 2100}, {"name": "Collateral", "table_name": "Collateral", "expands": ["CustomFieldValues", "CustomFieldValues/CustomField"], "selects": ["*", "CustomFieldValues/*", "CustomFieldValues/CustomField/name", "CustomFieldValues/CustomField/selectOptions"], "api_identifier": "Collateral?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": [], "incremental_filter_type": "int", "top_limit": 2000, "max_limit": 6000, "source_events_tracking": false, "source_events_tracking_id": "collateral_id", "source_events_table": "loanpro_events_loan_collateral"}, {"name": "Customers", "table_name": "Customers", "expands": ["Loans", "Phones", "CreditScore", "PrimaryAddress", "MailAddress", "CustomFieldValues", "CustomFieldValues/CustomField"], "api_identifier": "Customers?nopaging", "primary_key": "id", "incremental_key": "lastUpdate", "additional_filters": [], "incremental_filter_type": "datetime", "top_limit": 1000, "max_limit": 1000}, {"name": "Payments", "table_name": "Payments", "expands": ["PaymentType", "PaymentMethod", "PaymentInfo", "PaymentReverseTx"], "api_identifier": "Payments?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": [], "incremental_filter_type": "int", "top_limit": 3000, "max_limit": 9000, "customer_key": "CUSTOMER_MARINE_ONE", "source_events_tracking": true, "source_events_tracking_id": "payment_id", "source_events_table": "loanpro_events_payments_posted"}, {"name": "PaymentReversals", "table_name": "Payments", "expands": ["PaymentType", "PaymentMethod", "PaymentInfo", "PaymentReverseTx"], "api_identifier": "Payments?nopaging", "primary_key": "id", "incremental_key": "reverseDate", "additional_filters": [], "incremental_filter_type": "date", "top_limit": 3000, "max_limit": 9000, "skip_historical_resync": true, "source_events_tracking": false, "source_events_tracking_id": "payment_id", "source_events_table": "loanpro_events_payments_reversed"}, {"name": "LoanTransactions", "table_name": "LoanTransactions", "api_identifier": "LoanTransactions?nopaging", "primary_key": "id", "incremental_key": "id", "additional_filters": [], "incremental_filter_type": "int", "top_limit": 20000, "max_limit": 60000}, {"name": "LoanStatusArchive", "table_name": "LoanStatusArchive", "api_identifier": "LoanStatusArchive?nopaging", "primary_key": ["id", "date"], "incremental_key": "date", "additional_filters": [], "incremental_filter_type": "datetime", "top_limit": 10000, "max_limit": 30000}]}