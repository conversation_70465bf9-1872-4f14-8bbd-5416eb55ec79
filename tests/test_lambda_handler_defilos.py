import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '../src'))
from lambda_function import lambda_handler


def test_lambda_handler():
    with open(f"{os.path.dirname(__file__)}/test_lambda_handler_defilos_config.json", 'r', encoding='utf-8') as file:
        custom_config = json.load(file)
    with open(f"{os.path.dirname(__file__)}/test_lambda_handler_defilos_state.json", 'r', encoding='utf-8') as file:
        test_state = json.load(file)

    custom_config['resources'] = json.dumps(custom_config['resources'])
    event = {'bucket': custom_config['s3_bucket'], 'file': custom_config['s3_key'], 'secrets': {'api-key': '696D198D-0854-4B77-891F-101E0CEF01DA'}, 'customPayload': custom_config, 'agent': custom_config['agent'], 'setup_test': custom_config['setup_test']}

    print(f"\n\n{json.dumps(custom_config['resources'])}\n\n")
    print(event)
    result = lambda_handler(event, None)
    print(result)
    
    
if __name__ == "__main__":
    test_lambda_handler()
