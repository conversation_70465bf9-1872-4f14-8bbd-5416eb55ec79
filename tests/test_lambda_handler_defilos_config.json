{"partner_key": "defilos", "s3_key": "partners/defilos/test_api_process/", "s3_filename_identifier": "defilos_api", "s3_bucket": "customer-mvp", "api_base_url": "https://solera.defisolutions.com/dealerapi/", "agent": "Fivetran AWS Lambda Connector/fivetran/aws_lambda", "setup_test": false, "customer_key": "CUSTOMER_ARRA_FINANCE", "resources": [{"name": "Dealers", "table_name": "Dealers", "expands": [], "api_identifier": "Dealers", "additional_filters": ["?page={page_number}", "&pageSize={page_size}", "&withAllData=true"], "skip_incremental_filter": false, "historical_file_sync": true, "historical_sync_window_in_days": 2, "top_limit": 30, "max_limit": 30}]}