import sys
import os
import json

sys.path.append(os.path.join(os.path.dirname(__file__), '../src'))
from lambda_function import lambda_handler


def test_lambda_handler():
    with open(f"{os.path.dirname(__file__)}/test_lambda_handler_loanpro_config.json", 'r', encoding='utf-8') as file:
        custom_config = json.load(file)
    with open(f"{os.path.dirname(__file__)}/test_lambda_handler_loanpro_state.json", 'r', encoding='utf-8') as file:
        test_state = json.load(file)

    custom_config['resources'] = json.dumps(custom_config['resources'])
    tenant_id = ''
    api_key = ''
    event = {'state': test_state['state'], 'bucket': custom_config['s3_bucket'], 'file': custom_config['s3_key'], 'secrets': {'bearer': api_key, 'tenantid': tenant_id}, 'customPayload': custom_config, 'agent': custom_config['agent'], 'setup_test': custom_config['setup_test']}

    print(f"\n\n{json.dumps(custom_config['resources'])}\n\n")
    result = lambda_handler(event, None)
    print(result)


if __name__ == "__main__":
    test_lambda_handler()
